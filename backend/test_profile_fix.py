#!/usr/bin/env python3
"""
Test script to verify the profile fix for FacebookProfileMessenger
"""

import asyncio
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

async def test_profile_database_access():
    """Test profile database access"""
    print("🔍 Testing Profile Database Access")
    print("=" * 50)
    
    try:
        # Test database connection
        from app.core.database import get_db, init_db
        from app.models.profile import Profile
        from sqlalchemy import select
        
        print("✅ Database imports successful")
        
        # Initialize database
        await init_db()
        print("✅ Database initialized")
        
        # Test profile query
        async for db in get_db():
            try:
                result = await db.execute(select(Profile))
                profiles = result.scalars().all()
                print(f"✅ Found {len(profiles)} profiles in database")
                
                if profiles:
                    for profile in profiles:
                        print(f"   • Profile {profile.id}: {profile.name}")
                        print(f"     Path: {profile.profile_path}")
                        print(f"     Proxy: {profile.proxy_type}")
                else:
                    print("   ⚠️  No profiles found - create a profile first")
                
                break
            except Exception as e:
                print(f"❌ Database query failed: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_messenger_creation():
    """Test FacebookProfileMessenger creation"""
    print("\n🤖 Testing FacebookProfileMessenger Creation")
    print("-" * 50)
    
    try:
        from app.services.facebook_profile_messenger import FacebookProfileMessenger
        
        messenger = FacebookProfileMessenger()
        print("✅ FacebookProfileMessenger created successfully")
        
        # Test XPath selectors
        print(f"✅ Chat button XPath loaded: {len(messenger.CHAT_BUTTON_XPATH)} chars")
        print(f"✅ Message box XPath loaded: {len(messenger.MESSAGE_BOX_XPATH)} chars")
        print(f"✅ Send button XPath loaded: {len(messenger.SEND_BUTTON_XPATH)} chars")
        
        # Test profile manager integration
        if hasattr(messenger, 'profile_manager'):
            print("✅ Profile manager integrated")
        else:
            print("❌ Profile manager not found")
            
        if hasattr(messenger, 'camoufox_manager'):
            print("✅ Camoufox manager integrated")
        else:
            print("❌ Camoufox manager not found")
        
        return True
        
    except Exception as e:
        print(f"❌ Messenger creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_session_creation_logic():
    """Test session creation logic (without actual profile)"""
    print("\n🔧 Testing Session Creation Logic")
    print("-" * 50)
    
    try:
        from app.services.facebook_profile_messenger import FacebookProfileMessenger
        
        messenger = FacebookProfileMessenger()
        
        # Test with non-existent profile (should fail gracefully)
        print("Testing with non-existent profile ID 999...")
        session = await messenger.create_messaging_session(profile_id=999)
        
        if session is None:
            print("✅ Session creation correctly returned None for non-existent profile")
        else:
            print("⚠️  Session creation returned unexpected result")
        
        return True
        
    except Exception as e:
        print(f"❌ Session creation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_bulk_messaging_manager():
    """Test BulkMessagingManager integration"""
    print("\n📨 Testing BulkMessagingManager Integration")
    print("-" * 50)
    
    try:
        from app.services.bulk_messaging_manager import bulk_messaging_manager
        
        print("✅ BulkMessagingManager imported successfully")
        
        # Test messenger integration
        if hasattr(bulk_messaging_manager, 'messenger'):
            print("✅ FacebookProfileMessenger integrated in BulkMessagingManager")
            
            messenger = bulk_messaging_manager.messenger
            if hasattr(messenger, 'CHAT_BUTTON_XPATH'):
                print("✅ XPath selectors available in integrated messenger")
            else:
                print("❌ XPath selectors not found in integrated messenger")
        else:
            print("❌ FacebookProfileMessenger not found in BulkMessagingManager")
        
        return True
        
    except Exception as e:
        print(f"❌ BulkMessagingManager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Main test function"""
    print("🧪 Testing Profile Fix for Bulk Messaging")
    print("=" * 60)
    
    tests = [
        ("Database Access", test_profile_database_access),
        ("Messenger Creation", test_messenger_creation),
        ("Session Creation Logic", test_session_creation_logic),
        ("BulkMessagingManager", test_bulk_messaging_manager)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 30)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Profile fix is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    return passed == total


if __name__ == "__main__":
    asyncio.run(main())
