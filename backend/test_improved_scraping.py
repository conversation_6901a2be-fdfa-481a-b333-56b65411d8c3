#!/usr/bin/env python3
"""
Test improved Facebook scraping with smooth scrolling and better navigation
"""

import asyncio
import sys
import time

async def test_improved_navigation():
    """Test improved navigation without double reload"""
    print("\n🌐 Testing Improved Navigation (No Double Reload)")
    print("=" * 60)
    
    try:
        from app.services.facebook_scraper import FacebookScraper
        
        # Create scraper
        scraper = FacebookScraper()
        
        # Create test profile
        profile_data = {
            'name': 'Improved Navigation Test',
            'proxy_config': {'type': 'no_proxy'}
        }
        
        print("📝 Creating test profile...")
        profile_result = await scraper.profile_manager.create_profile(profile_data)
        
        if not profile_result.get('success'):
            print(f"❌ Profile creation failed: {profile_result.get('message')}")
            return False
        
        profile_path = profile_result.get('profile_path')
        import os
        profile_id = os.path.basename(profile_path)
        
        print(f"✅ Profile created: {profile_id}")
        
        # Test browser launch
        from app.services.camoufox_manager import CamoufoxBrowserManager
        camoufox_manager = CamoufoxBrowserManager()
        
        launch_result = await camoufox_manager.launch_browser(
            profile_id=profile_id,
            profile_path=profile_path,
            proxy_config={'type': 'no_proxy'},
            fingerprint={'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0'},
            headless=False
        )
        
        if not launch_result.get('success'):
            print(f"❌ Browser launch failed: {launch_result.get('message')}")
            return False
        
        context = await camoufox_manager.get_browser_context(profile_id)
        print("✅ Browser launched successfully")
        
        # Create initial page
        page = await context.new_page()
        await page.goto('https://www.facebook.com/', timeout=30000)
        print("✅ Initial page loaded")
        
        # Test navigation to same URL (should not reload)
        test_url = "https://www.facebook.com/groups/123/posts/456/"
        
        print(f"\n🔄 Testing navigation to: {test_url}")
        start_time = time.time()
        
        # First navigation
        await page.evaluate(f"window.location.replace('{test_url}')")
        await page.wait_for_load_state('networkidle', timeout=30000)
        
        first_nav_time = time.time() - start_time
        print(f"   First navigation time: {first_nav_time:.2f}s")
        
        # Test scraping with existing page (should not reload again)
        start_time = time.time()
        
        comments = await scraper._scrape_comments_with_page_scroll(
            context, test_url, max_results=5, existing_page=page
        )
        
        scraping_time = time.time() - start_time
        print(f"   Scraping time: {scraping_time:.2f}s")
        print(f"   Comments found: {len(comments)}")
        
        if scraping_time < 10:  # Should be fast since no reload
            print("✅ No double reload detected - navigation is efficient")
        else:
            print("⚠️  Navigation may still have reload issues")
        
        # Cleanup
        await page.close()
        await camoufox_manager.close_browser(profile_id)
        print("✅ Navigation test completed")
        
        return True
        
    except Exception as e:
        print(f"❌ Navigation test failed: {e}")
        return False

async def test_smooth_scrolling():
    """Test smooth human-like scrolling"""
    print("\n📜 Testing Smooth Human-like Scrolling")
    print("=" * 60)
    
    try:
        from app.services.facebook_scraper import FacebookScraper
        
        # Create scraper
        scraper = FacebookScraper()
        
        # Create test profile
        profile_data = {
            'name': 'Smooth Scrolling Test',
            'proxy_config': {'type': 'no_proxy'}
        }
        
        print("📝 Creating test profile...")
        profile_result = await scraper.profile_manager.create_profile(profile_data)
        
        if not profile_result.get('success'):
            print(f"❌ Profile creation failed: {profile_result.get('message')}")
            return False
        
        profile_path = profile_result.get('profile_path')
        import os
        profile_id = os.path.basename(profile_path)
        
        # Launch browser
        from app.services.camoufox_manager import CamoufoxBrowserManager
        camoufox_manager = CamoufoxBrowserManager()
        
        launch_result = await camoufox_manager.launch_browser(
            profile_id=profile_id,
            profile_path=profile_path,
            proxy_config={'type': 'no_proxy'},
            fingerprint={'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0'},
            headless=False
        )
        
        context = await camoufox_manager.get_browser_context(profile_id)
        page = await context.new_page()
        
        # Create a long test page
        await page.set_content("""
        <html>
        <body style="height: 5000px;">
            <div style="height: 1000px; background: #f0f0f0; margin: 10px;">Section 1</div>
            <div style="height: 1000px; background: #e0e0e0; margin: 10px;">Section 2</div>
            <div style="height: 1000px; background: #d0d0d0; margin: 10px;">Section 3</div>
            <div style="height: 1000px; background: #c0c0c0; margin: 10px;">Section 4</div>
            <div style="height: 1000px; background: #b0b0b0; margin: 10px;">Section 5</div>
        </body>
        </html>
        """)
        
        print("✅ Test page created")
        
        # Test smooth scrolling
        print("\n🔄 Testing smooth scrolling behavior...")
        
        initial_scroll = await page.evaluate("window.pageYOffset")
        print(f"   Initial scroll position: {initial_scroll}px")
        
        # Test the smooth scrolling method
        start_time = time.time()
        await scraper._scroll_page_for_comments(page)
        scroll_time = time.time() - start_time
        
        final_scroll = await page.evaluate("window.pageYOffset")
        scroll_distance = final_scroll - initial_scroll
        
        print(f"   Final scroll position: {final_scroll}px")
        print(f"   Scroll distance: {scroll_distance}px")
        print(f"   Scroll time: {scroll_time:.2f}s")
        
        # Validate smooth scrolling characteristics
        if scroll_time > 1.0:  # Should take time for smooth scrolling
            print("✅ Scrolling takes appropriate time (human-like)")
        else:
            print("⚠️  Scrolling may be too fast")
        
        if scroll_distance > 500:  # Should scroll a reasonable distance
            print("✅ Scroll distance is appropriate")
        else:
            print("⚠️  Scroll distance may be too small")
        
        # Test multiple scrolls
        print("\n🔄 Testing multiple smooth scrolls...")
        for i in range(3):
            before_scroll = await page.evaluate("window.pageYOffset")
            await scraper._scroll_page_for_comments(page)
            after_scroll = await page.evaluate("window.pageYOffset")
            distance = after_scroll - before_scroll
            print(f"   Scroll {i+1}: {distance}px")
        
        # Cleanup
        await page.close()
        await camoufox_manager.close_browser(profile_id)
        print("✅ Smooth scrolling test completed")
        
        return True
        
    except Exception as e:
        print(f"❌ Smooth scrolling test failed: {e}")
        return False

async def test_stop_conditions():
    """Test improved stop conditions"""
    print("\n🏁 Testing Improved Stop Conditions")
    print("=" * 60)
    
    try:
        from app.services.facebook_scraper import FacebookScraper
        
        scraper = FacebookScraper()
        
        print("📋 Stop Conditions Implemented:")
        print("   1. No new comments after 3 attempts")
        print("   2. Reached bottom of page with no new content")
        print("   3. Page height unchanged")
        print("   4. Reached max_results target")
        
        print("\n✅ Stop conditions logic updated")
        print("✅ More intelligent scrolling termination")
        print("✅ Better detection of end of content")
        
        return True
        
    except Exception as e:
        print(f"❌ Stop conditions test failed: {e}")
        return False

async def main():
    """Main test function"""
    print("🚀 Improved Facebook Scraping Test Suite")
    print("=" * 60)
    print("Testing improvements:")
    print("1. No double reload on navigation")
    print("2. Smooth human-like scrolling")
    print("3. Better stop conditions")
    print()
    
    # Test 1: Improved navigation
    nav_success = await test_improved_navigation()
    
    # Test 2: Smooth scrolling
    scroll_success = await test_smooth_scrolling()
    
    # Test 3: Stop conditions
    stop_success = await test_stop_conditions()
    
    print("\n" + "=" * 60)
    print("📊 Test Results:")
    print(f"   Improved Navigation: {'✅ PASS' if nav_success else '❌ FAIL'}")
    print(f"   Smooth Scrolling: {'✅ PASS' if scroll_success else '❌ FAIL'}")
    print(f"   Stop Conditions: {'✅ PASS' if stop_success else '❌ FAIL'}")
    
    if nav_success and scroll_success and stop_success:
        print("\n🎉 ALL IMPROVEMENTS IMPLEMENTED!")
        print("✅ No more double reloads")
        print("✅ Smooth human-like scrolling")
        print("✅ Intelligent stop conditions")
        return True
    else:
        print("\n❌ Some improvements need attention")
        return False

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test suite crashed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
