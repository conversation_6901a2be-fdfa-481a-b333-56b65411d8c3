#!/usr/bin/env python3
"""
Test script to verify the open_facebook fix for FacebookProfileMessenger
"""

import asyncio
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

async def test_camoufox_manager_methods():
    """Test CamoufoxBrowserManager available methods"""
    print("🔍 Testing CamoufoxBrowserManager Methods")
    print("=" * 50)
    
    try:
        from app.services.camoufox_manager import CamoufoxBrowserManager
        
        manager = CamoufoxBrowserManager()
        print("✅ CamoufoxBrowserManager created successfully")
        
        # Check available methods
        methods = [method for method in dir(manager) if not method.startswith('_') and callable(getattr(manager, method))]
        print(f"✅ Available methods: {len(methods)}")
        
        for method in methods:
            print(f"   • {method}")
        
        # Check if open_facebook exists
        if hasattr(manager, 'open_facebook'):
            print("❌ open_facebook method found (this should not exist)")
        else:
            print("✅ open_facebook method correctly not found")
        
        # Check essential methods
        essential_methods = ['launch_browser', 'close_browser', 'get_browser_status', 'get_browser_context']
        for method in essential_methods:
            if hasattr(manager, method):
                print(f"✅ {method} method available")
            else:
                print(f"❌ {method} method missing")
        
        return True
        
    except Exception as e:
        print(f"❌ CamoufoxBrowserManager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_messenger_session_creation():
    """Test FacebookProfileMessenger session creation logic"""
    print("\n🤖 Testing FacebookProfileMessenger Session Creation")
    print("-" * 50)
    
    try:
        from app.services.facebook_profile_messenger import FacebookProfileMessenger
        
        messenger = FacebookProfileMessenger()
        print("✅ FacebookProfileMessenger created successfully")
        
        # Check if it has the required managers
        if hasattr(messenger, 'camoufox_manager'):
            print("✅ CamoufoxBrowserManager integrated")
        else:
            print("❌ CamoufoxBrowserManager not found")
            
        if hasattr(messenger, 'profile_manager'):
            print("✅ AntidetectProfileManager integrated")
        else:
            print("❌ AntidetectProfileManager not found")
        
        # Test session creation with non-existent profile (should fail gracefully)
        print("\n🔧 Testing session creation with non-existent profile...")
        session = await messenger.create_messaging_session(profile_id=999)
        
        if session is None:
            print("✅ Session creation correctly returned None for non-existent profile")
        else:
            print("⚠️  Session creation returned unexpected result")
        
        return True
        
    except Exception as e:
        print(f"❌ Messenger session test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_messaging_workflow_methods():
    """Test messaging workflow methods"""
    print("\n🎭 Testing Messaging Workflow Methods")
    print("-" * 50)
    
    try:
        from app.services.facebook_profile_messenger import FacebookProfileMessenger
        
        messenger = FacebookProfileMessenger()
        
        # Test URL building
        test_urls = [
            "https://www.facebook.com/john.doe",
            "/john.doe",
            "john.doe",
            "/groups/123/user/456/"
        ]
        
        print("✅ Testing URL building:")
        for url in test_urls:
            built_url = messenger._build_profile_url(url)
            print(f"   {url} → {built_url}")
        
        # Test XPath selectors
        print("\n✅ Testing XPath selectors:")
        print(f"   Chat Button: {len(messenger.CHAT_BUTTON_XPATH)} chars")
        print(f"   Message Box: {len(messenger.MESSAGE_BOX_XPATH)} chars")
        print(f"   Send Button: {len(messenger.SEND_BUTTON_XPATH)} chars")
        
        # Check if all required methods exist
        required_methods = [
            '_simulate_profile_viewing',
            '_click_message_button', 
            '_type_and_send_message',
            '_type_message_humanlike',
            '_is_session_healthy',
            '_cleanup_session'
        ]
        
        print("\n✅ Testing required methods:")
        for method in required_methods:
            if hasattr(messenger, method):
                print(f"   ✅ {method}")
            else:
                print(f"   ❌ {method} missing")
        
        return True
        
    except Exception as e:
        print(f"❌ Workflow methods test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_database_profile_access():
    """Test database profile access"""
    print("\n🗄️ Testing Database Profile Access")
    print("-" * 50)
    
    try:
        from app.core.database import get_db
        from app.models.profile import Profile
        from sqlalchemy import select
        
        # Test database connection
        async for db in get_db():
            try:
                result = await db.execute(select(Profile))
                profiles = result.scalars().all()
                print(f"✅ Found {len(profiles)} profiles in database")
                
                if profiles:
                    test_profile = profiles[0]
                    print(f"✅ Test profile: {test_profile.name} (ID: {test_profile.id})")
                    print(f"   Path: {test_profile.profile_path}")
                    print(f"   Proxy: {test_profile.proxy_type}")
                    
                    # Test profile data mapping
                    proxy_config = {'type': 'no_proxy'}
                    if test_profile.proxy_type and test_profile.proxy_type != 'no_proxy':
                        proxy_config = {
                            'type': test_profile.proxy_type,
                            'host': test_profile.proxy_host,
                            'port': test_profile.proxy_port,
                            'username': test_profile.proxy_username,
                            'password': test_profile.proxy_password
                        }
                    
                    print(f"✅ Proxy config mapped: {proxy_config}")
                    print(f"✅ Fingerprint data: {'Available' if test_profile.fingerprint_data else 'None'}")
                    
                else:
                    print("⚠️  No profiles found - create a profile first")
                
                break
            except Exception as e:
                print(f"❌ Database query failed: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Main test function"""
    print("🧪 Testing open_facebook Fix")
    print("=" * 60)
    
    tests = [
        ("CamoufoxBrowserManager Methods", test_camoufox_manager_methods),
        ("Messenger Session Creation", test_messenger_session_creation),
        ("Messaging Workflow Methods", test_messaging_workflow_methods),
        ("Database Profile Access", test_database_profile_access)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 30)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! open_facebook fix is working correctly.")
        print("\n✅ Fixed Issues:")
        print("   • Removed non-existent open_facebook() call")
        print("   • Added direct Facebook navigation after browser launch")
        print("   • Proper error handling for navigation failures")
        print("   • Maintained session structure and functionality")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    return passed == total


if __name__ == "__main__":
    asyncio.run(main())
