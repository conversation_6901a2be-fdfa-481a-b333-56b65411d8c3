#!/usr/bin/env python3
"""
Test XPath scraping với profile test1 và bài post Facebook thực tế
"""

import asyncio
import sys
import json

async def test_xpath_scraping():
    """Test XPath scraping với profile test1"""
    print("\n🔧 Testing XPath Scraping with Profile test1")
    print("=" * 60)

    try:
        from app.services.facebook_scraper import FacebookScraper, ScrapingType
        from app.services.camoufox_manager import CamoufoxBrowserManager

        # Create scraper
        scraper = FacebookScraper()

        # URL bài post cần test
        post_url = "https://www.facebook.com/groups/comailo/posts/622067077593976/"

        print(f"📍 Target post URL: {post_url}")
        print(f"📋 Testing XPath selectors from comment.html")

        # Tìm profile test1
        print(f"🔍 Looking for profile test1...")

        # L<PERSON>y danh sách profiles từ database
        from app.core.database import AsyncSessionLocal
        from app.models.profile import Profile
        from sqlalchemy import select

        async with AsyncSessionLocal() as db:
            result = await db.execute(select(Profile))
            profiles = result.scalars().all()

        test1_profile = None
        for profile in profiles:
            if profile.name == 'test1':
                test1_profile = profile
                break

        if not test1_profile:
            print(f"❌ Profile test1 not found. Available profiles:")
            for profile in profiles:
                print(f"   - {profile.name} (ID: {profile.id})")
            return False

        print(f"✅ Found profile test1 (ID: {test1_profile.id})")

        # Launch browser với profile test1
        camoufox_manager = CamoufoxBrowserManager()

        print(f"🚀 Launching browser with profile test1...")
        launch_result = await camoufox_manager.launch_browser(
            profile_id=str(test1_profile.id),
            profile_path=test1_profile.profile_path,
            proxy_config={'type': 'no_proxy'},
            fingerprint={'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0'},
            headless=False
        )

        if not launch_result.get('success'):
            print(f"❌ Failed to launch browser: {launch_result.get('message')}")
            return False

        print(f"✅ Browser launched successfully")

        # Tạo crawler wrapper
        context = await camoufox_manager.get_browser_context(str(test1_profile.id))

        class TestCrawlerWrapper:
            def __init__(self, context, profile, task_id):
                self.context = context
                self.profile = profile
                self.task_id = task_id
                self.crawler_strategy = TestCrawlerStrategy(context)

            async def arun(self, url, config=None):
                """Navigate to URL and return page content"""
                try:
                    page = await self.crawler_strategy.get_page()
                    await page.goto(url, wait_until='networkidle')

                    html = await page.content()

                    return type('CrawlResult', (), {
                        'success': True,
                        'html': html,
                        'extracted_content': [],
                        'error_message': None,
                        'url': page.url
                    })()

                except Exception as e:
                    return type('CrawlResult', (), {
                        'success': False,
                        'html': '',
                        'extracted_content': [],
                        'error_message': str(e),
                        'url': url
                    })()

        class TestCrawlerStrategy:
            def __init__(self, context):
                self._context = context
                self._page = None

            async def get_page(self):
                if not self._page:
                    self._page = await self._context.new_page()
                return self._page

            @property
            def page(self):
                return self._page

        crawler = TestCrawlerWrapper(context, test1_profile, 'test-task-id')

        # Test scraping
        print(f"\n🚀 Starting Facebook post scraping...")
        result = await scraper.scrape_facebook_post(
            crawler,
            post_url,
            [ScrapingType.COMMENTS],
            max_results=10
        )

        print(f"\n📊 Scraping Results:")
        print(f"   Comments found: {len(result.get('comments', []))}")
        print(f"   Total found: {result.get('total_found', 0)}")
        print(f"   Errors: {len(result.get('errors', []))}")

        if result.get('errors'):
            print(f"   Error details: {result.get('errors')}")

        # Hiển thị một vài comment đầu tiên
        comments = result.get('comments', [])
        for i, comment in enumerate(comments[:3]):
            print(f"\n💬 Comment {i+1}:")
            print(f"   👤 User: {comment.get('full_name', 'Unknown')}")
            print(f"   🆔 UID: {comment.get('facebook_uid', 'Unknown')}")
            print(f"   🔗 Profile: {comment.get('profile_url', 'Unknown')}")
            print(f"   💭 Content: {comment.get('interaction_content', 'Unknown')[:100]}...")

        if len(comments) > 3:
            print(f"\n... và {len(comments) - 3} comment khác")

        # Kiểm tra chất lượng dữ liệu
        valid_comments = [c for c in comments if c.get('facebook_uid') and c.get('full_name')]
        print(f"\n📈 Data Quality:")
        print(f"   ✅ Valid comments (có UID + name): {len(valid_comments)}/{len(comments)}")
        print(f"   📊 Success rate: {len(valid_comments)/len(comments)*100:.1f}%" if comments else "0%")

        # Cleanup
        await camoufox_manager.close_browser(str(test1_profile.id))
        print(f"✅ Browser closed")

        return len(valid_comments) > 0
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_xpath_selectors():
    """Test XPath selectors trực tiếp"""
    print("\n🔍 Testing XPath Selectors")
    print("=" * 60)
    
    try:
        from app.services.facebook_scraper import FacebookScraper
        
        scraper = FacebookScraper()
        
        print("📋 Current XPath Selectors:")
        for key, value in scraper.page_selectors.items():
            print(f"   {key}: {value}")
        
        print("\n✅ XPath selectors loaded successfully")
        return True
        
    except Exception as e:
        print(f"❌ Failed to load XPath selectors: {e}")
        return False

async def main():
    """Main test function"""
    print("🔧 XPath Scraping Test with Profile test1")
    print("=" * 60)
    print("Testing:")
    print("1. XPath selectors from comment.html")
    print("2. Real Facebook post scraping")
    print("3. Data extraction quality")
    print()
    
    # Test 1: XPath selectors
    xpath_success = await test_xpath_selectors()
    
    if not xpath_success:
        print("❌ XPath selectors test failed, stopping")
        return False
    
    # Test 2: Real scraping
    scraping_success = await test_xpath_scraping()
    
    print("\n" + "=" * 60)
    print("📊 Test Results:")
    print(f"   XPath Selectors: {'✅ PASS' if xpath_success else '❌ FAIL'}")
    print(f"   Real Scraping: {'✅ PASS' if scraping_success else '❌ FAIL'}")
    
    if xpath_success and scraping_success:
        print("\n🎉 XPATH SCRAPING TEST SUCCESSFUL!")
        print("✅ XPath selectors working correctly")
        print("✅ Data extraction successful")
        print("✅ User data crawled successfully")
        return True
    else:
        print("\n❌ XPath scraping test needs improvement")
        if xpath_success and not scraping_success:
            print("💡 XPath selectors are correct but scraping failed")
            print("💡 Check browser/profile issues or Facebook changes")
        return False

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test suite crashed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
