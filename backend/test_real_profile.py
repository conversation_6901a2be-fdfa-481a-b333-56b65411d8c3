#!/usr/bin/env python3
"""
Test script to verify messaging with real profile
"""

import asyncio
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

async def test_with_real_profile():
    """Test messaging session creation with real profile"""
    print("🧪 Testing with Real Profile")
    print("=" * 50)
    
    try:
        # Get database connection
        from app.core.database import get_db
        from app.models.profile import Profile
        from sqlalchemy import select
        
        # Find first available profile
        async for db in get_db():
            try:
                result = await db.execute(select(Profile))
                profiles = result.scalars().all()
                break
            except Exception as e:
                print(f"❌ Database error: {e}")
                return False
        
        if not profiles:
            print("❌ No profiles found in database")
            print("   Create a profile first via the web interface")
            return False
        
        # Use first profile
        test_profile = profiles[0]
        print(f"✅ Using profile: {test_profile.name} (ID: {test_profile.id})")
        print(f"   Path: {test_profile.profile_path}")
        print(f"   Proxy: {test_profile.proxy_type}")
        
        # Test messenger session creation
        from app.services.facebook_profile_messenger import FacebookProfileMessenger
        
        messenger = FacebookProfileMessenger()
        print("✅ FacebookProfileMessenger created")
        
        # Test session creation (this will try to launch browser)
        print(f"\n🚀 Testing session creation with profile {test_profile.id}...")
        print("   Note: This will try to launch Camoufox browser")
        
        session = await messenger.create_messaging_session(profile_id=test_profile.id)
        
        if session:
            print("✅ Session created successfully!")
            print(f"   Session type: {type(session)}")
            print(f"   Session keys: {list(session.keys()) if isinstance(session, dict) else 'N/A'}")
            
            # Clean up session
            await messenger._cleanup_session(test_profile.id)
            print("✅ Session cleaned up")
            
            return True
        else:
            print("⚠️  Session creation returned None")
            print("   This is expected if Camoufox is not properly installed")
            print("   or if the profile path doesn't exist")
            return False
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_messaging_workflow_simulation():
    """Test the messaging workflow without actual browser"""
    print("\n🎭 Testing Messaging Workflow (Simulation)")
    print("-" * 50)
    
    try:
        from app.services.facebook_profile_messenger import FacebookProfileMessenger
        
        messenger = FacebookProfileMessenger()
        
        # Test URL building
        test_urls = [
            "https://www.facebook.com/john.doe",
            "/john.doe",
            "john.doe",
            "/groups/123/user/456/"
        ]
        
        print("✅ Testing URL building:")
        for url in test_urls:
            built_url = messenger._build_profile_url(url)
            print(f"   {url} → {built_url}")
        
        # Test XPath selectors
        print("\n✅ Testing XPath selectors:")
        print(f"   Chat Button: {messenger.CHAT_BUTTON_XPATH[:50]}...")
        print(f"   Message Box: {messenger.MESSAGE_BOX_XPATH[:50]}...")
        print(f"   Send Button: {messenger.SEND_BUTTON_XPATH[:50]}...")
        
        # Test rate limiter
        from app.services.facebook_profile_messenger import RateLimiter
        
        rate_limiter = RateLimiter(requests_per_minute=5, requests_per_hour=100)
        print("\n✅ Testing rate limiter:")
        
        can_send = await rate_limiter.can_send()
        print(f"   Can send message: {can_send}")
        
        if can_send:
            await rate_limiter.record_request()
            print("   ✅ Request recorded")
        
        return True
        
    except Exception as e:
        print(f"❌ Workflow test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_bulk_messaging_config():
    """Test bulk messaging configuration"""
    print("\n📨 Testing Bulk Messaging Configuration")
    print("-" * 50)
    
    try:
        from app.models.messaging import BulkMessagingConfig, MessageType
        
        # Create test config
        config = BulkMessagingConfig(
            name="Test Messaging Task",
            sender_profile_id=1,
            excel_file_path="/tmp/test_recipients.xlsx",
            message_content="Hello! This is a test message from the updated system.",
            message_type=MessageType.TEXT,
            delay_between_messages_min=10,
            delay_between_messages_max=30,
            stop_on_consecutive_failures=3,
            skip_sent_recipients=True
        )
        
        print("✅ BulkMessagingConfig created successfully:")
        print(f"   Name: {config.name}")
        print(f"   Sender Profile ID: {config.sender_profile_id}")
        print(f"   Message: {config.message_content[:50]}...")
        print(f"   Delay range: {config.delay_between_messages_min}-{config.delay_between_messages_max}s")
        print(f"   Stop after failures: {config.stop_on_consecutive_failures}")
        print(f"   Skip sent recipients: {config.skip_sent_recipients}")
        
        return True
        
    except Exception as e:
        print(f"❌ Config test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Main test function"""
    print("🧪 Testing Real Profile Integration")
    print("=" * 60)
    
    tests = [
        ("Real Profile Session", test_with_real_profile),
        ("Workflow Simulation", test_messaging_workflow_simulation),
        ("Bulk Messaging Config", test_bulk_messaging_config)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 30)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! System is ready for real messaging.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    print("\n🚀 Next Steps:")
    print("   1. Create Excel file with recipient data (full_name, profile_url)")
    print("   2. Test bulk messaging via web interface")
    print("   3. Monitor browser actions to verify XPath selectors")
    print("   4. Check message delivery success rates")
    
    return passed == total


if __name__ == "__main__":
    asyncio.run(main())
